package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"os"
	"os/exec"
	"strconv"
	"strings"
)

type OperationService struct {
	clusterRepo           ports.ClusterRepository
	jobService            ports.JobService
	clusterService        ports.ClusterService
	deploymentService     ports.DeploymentService
	serviceService        ports.ServiceService
	ingressService        ports.IngressService
	namespaceService      ports.NamespaceService
	DnsService            ports.DnsService
	orderService          ports.OrderService
	orderNamespaceService ports.OrderNamespaceService
	domainRepo            ports.DomainRepository
}

func NewOperationService(
	clusterRepo ports.ClusterRepository,
	jobService ports.JobService,
	cluster ports.ClusterService,
	deployment ports.DeploymentService,
	service ports.ServiceService,
	ingress ports.IngressService,
	namespace ports.NamespaceService,
	dnsService ports.DnsService,
	orderService ports.OrderService,
	orderNamespaceService ports.OrderNamespaceService,
	domainRepo ports.DomainRepository,
) ports.OperationService {
	return &OperationService{
		clusterRepo:           clusterRepo,
		jobService:            jobService,
		clusterService:        cluster,
		deploymentService:     deployment,
		serviceService:        service,
		ingressService:        ingress,
		namespaceService:      namespace,
		DnsService:            dnsService,
		orderService:          orderService,
		orderNamespaceService: orderNamespaceService,
		domainRepo:            domainRepo,
	}
}

type JobRequest struct {
	ID          *uint64          `json:"id"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	StatusID    uint64           `json:"status_id"`
	EventID     *uint64          `json:"event_id,omitempty"`
	Event       domain.JobEvent  `json:"event,omitempty"`
	Action      domain.JobAction `json:"action,omitempty"`
}

// TerraformOutput represents a single Terraform output value
type TerraformOutput struct {
	Value     interface{} `json:"value"`
	Type      interface{} `json:"type"`
	Sensitive bool        `json:"sensitive"`
}

// TerraformOutputs represents all Terraform outputs
type TerraformOutputs map[string]TerraformOutput

func (s *OperationService) GetOperationData(clusterId, namespaceId uint64) (dto.OperationData, error) {
	cluster, err := s.clusterRepo.FindByID(clusterId, namespaceId)
	if err != nil {
		return dto.OperationData{}, err
	}
	if cluster == nil {
		return dto.OperationData{}, errors.New("cluster not found")
	}

	// find default domain
	isDefault := true
	filter := &ports.DomainFilter{
		NamespaceID: &namespaceId,
		IsDefault:   &isDefault,
	}

	domains, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return dto.OperationData{}, err
	}

	if len(domains) == 0 {
		return dto.OperationData{}, errors.New("no default domain found for this namespace")
	}

	return dto.ToOperationDataDTO(*cluster, domains[0].Name), nil
}

func (c *OperationService) CreateOperationAsync(userID uint64, accessToken string, req dto.OperationCreateReq) (interface{}, error) {
	eventID := req.NamespaceID

	var Job JobRequest

	if req.Method == "apply" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Create project",
			Description: "Create the project to digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "namespace",
			Action:      "create",
		}
	} else if req.Method == "destroy" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Destroy project",
			Description: "Destroy the project from digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "namespace",
			Action:      "delete",
		}
	} else if req.Method == "plan" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Plan project",
			Description: "Plan the project to digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "namespace",
			Action:      "plan",
		}
	}

	job, err := c.jobService.CreateJob(
		userID,
		Job.Name,
		Job.Description,
		Job.StatusID,
		Job.EventID,
		Job.Event,
		Job.Action,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to project job: %v", err)
	}
	Job.ID = &job.ID

	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Recovered from panic in CreateOperationAsync: %v\n", r)
			}
		}()
		result, err := c.executeCreateOperation(accessToken, req, Job, userID)
		if err != nil {
			fmt.Printf("Async project operation failed: %v\n", err)
		} else {
			fmt.Printf("Async project operation completed successfully: %v\n", result)
		}
	}()
	return "projects start create process.", nil
}

func (c *OperationService) executeCreateOperation(accessToken string, req dto.OperationCreateReq, Job JobRequest, userID uint64) (interface{}, error) {
	// Update job status to running (3)
	if err := c.updateJob(Job, userID, 3); err != nil {
		return nil, err
	}
	var statusID uint64
	if Job.Action == "create" {
		statusID = 2
	}
	if Job.Action == "plan" {
		statusID = 1
	}
	if Job.Action == "update" {
		statusID = 5
	}
	if Job.Action == "delete" {
		statusID = 7
	}
	err := c.UpdateClusterNamespaceStatuses(statusID, req.ClusterID, req.NamespaceID, userID)
	if err != nil {
		return nil, err
	}
	valueEndpoint := os.Getenv("TF_OPERATION_ENDPOINT")
	endpoint := fmt.Sprintf("%s/operations/%s?namespace_id=%s", valueEndpoint, strconv.Itoa(int(req.ClusterID)), strconv.Itoa(int(req.NamespaceID)))
	terraformDir := "./terraform/resource"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/resource"
	}
	workspaceName := fmt.Sprintf("namespace-%s", strconv.Itoa(int(req.NamespaceID)))

	// export and get ENV
	envDoToken := os.Getenv("DO_TOKEN")
	valueCloudflareApiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	valueCloudflareMasterZoneId := os.Getenv("CLOUDFLARE_MASTER_ZONE_ID")
	setEnvDoToken, setEnvEndpoint, setEnvApiToken, setEnvCloudflareApiToken, setEnvCloudflareMasterZoneId :=
		os.Setenv("TF_VAR_do_token", envDoToken),
		os.Setenv("TF_VAR_operation_endpoint", endpoint),
		os.Setenv("TF_VAR_access_token", accessToken),
		os.Setenv("TF_VAR_cloudflare_api_token", valueCloudflareApiToken),
		os.Setenv("TF_VAR_cloudflare_zone_id", valueCloudflareMasterZoneId)
	if setEnvDoToken != nil {
		fmt.Println("set env do token failed", setEnvDoToken)
		return "set env do token failed", setEnvDoToken
	}
	if setEnvEndpoint != nil {
		fmt.Println("set env endpoint failed", setEnvEndpoint)
		return "set env endpoint failed", setEnvEndpoint
	}
	if setEnvApiToken != nil {
		fmt.Println("set env api token failed", setEnvApiToken)
		return "set env api token failed", setEnvApiToken
	}
	if setEnvCloudflareApiToken != nil {
		fmt.Println("set env cloudflare api token failed", setEnvCloudflareApiToken)
		return "set env cloudflare api token failed", setEnvCloudflareApiToken
	}
	if setEnvCloudflareMasterZoneId != nil {
		fmt.Println("set env cloudflare master zone id failed", setEnvCloudflareMasterZoneId)
		return "set env cloudflare master zone id failed", setEnvCloudflareMasterZoneId
	}

	valueDB := os.Getenv("TF_BACKEND_DB")
	valueSchema := os.Getenv("TF_BACKEND_SCHEMA_RESOURCE")
	// terraform init
	connStr := fmt.Sprintf("-backend-config=conn_str=%s", valueDB)
	schemaName := fmt.Sprintf("-backend-config=schema_name=%s", valueSchema)
	tfInit := exec.Command("terraform", "init",
		"-lock=false",
		connStr,
		schemaName,
	)
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()
	if tfInitErr != nil {
		fmt.Printf("terraform init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform init failed - error: %v\n", tfInitErr)

		// Update job status to failed (4) when terraform init fails
		if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
			fmt.Printf("failed to update job to failed status: %v\n", updateErr)
		}
		if err := c.updateClusterStatus(req.ClusterID, 8); err != nil {
			fmt.Printf("failed to update cluster status: %v\n", err)
		}

		return "terraform init failed", tfInitErr
	}
	fmt.Println("terraform init successful", string(tfInitOutput))
	// terraform select workspace or create workspace
	tfWorkspace := exec.Command("terraform", "workspace", "select", "-or-create", workspaceName)
	tfWorkspace.Dir = terraformDir
	tfWorkspaceOutput, tfWorkspaceErr := tfWorkspace.CombinedOutput()
	if tfWorkspaceErr != nil {
		fmt.Println("select or create workspace failed", string(tfWorkspaceOutput))
		return "select or create workspace failed", tfWorkspaceErr
	}
	fmt.Println("select or create workspace successful", string(tfWorkspaceOutput))
	// terraform plan
	if req.Method == "plan" {
		tfPlan := exec.Command("terraform", "plan", "-lock=false")
		tfPlan.Dir = terraformDir
		tfPlanOutput, tfPlanErr := tfPlan.CombinedOutput()
		if tfPlanErr != nil {
			fmt.Println("terraform plan failed", string(tfPlanOutput))
			return "terraform plan failed", tfPlanErr
		}
		fmt.Println("terraform plan successful", string(tfPlanOutput))
	}
	//terraform apply
	if req.Method == "destroy" {
		// terraform destroy
		tfDestroy := exec.Command("terraform", "destroy", "-auto-approve", "-lock=false")
		tfDestroy.Dir = terraformDir
		tfDestroyOutput, tfDestroyErr := tfDestroy.CombinedOutput()
		if tfDestroyErr != nil {
			fmt.Println("terraform destroy failed", string(tfDestroyOutput))
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			return "terraform destroy failed", tfDestroyErr
		}
		fmt.Println("terraform destroy successful", string(tfDestroyOutput))
		// terraform delete workspace
		tfDefaultWorkspace := exec.Command("terraform", "workspace", "select", "-or-create", "default")
		tfDefaultWorkspace.Dir = terraformDir
		tfDefaultWorkspaceOutput, tfDefaultWorkspaceErr := tfDefaultWorkspace.CombinedOutput()
		if tfDefaultWorkspaceErr != nil {
			fmt.Println("select or create default workspace failed", string(tfDefaultWorkspaceOutput))
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			return "select or create default workspace failed", tfDefaultWorkspaceErr
		}
		fmt.Println("select or create default workspace successful", string(tfDefaultWorkspaceOutput))
		tfDeleteWorkspace := exec.Command("terraform", "workspace", "delete", workspaceName)
		tfDeleteWorkspace.Dir = terraformDir
		tfDeleteWorkspaceOutput, tfDeleteWorkspaceErr := tfDeleteWorkspace.CombinedOutput()
		if tfDeleteWorkspaceErr != nil {
			fmt.Println("delete workspace failed", string(tfDeleteWorkspaceOutput))
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			return "delete workspace failed", tfDeleteWorkspaceErr
		}
		fmt.Println("delete workspace successful", string(tfDeleteWorkspaceOutput))
		//setStatus, statusErr := c.GenerateRepository.UpdateShopStatus(shop.ShopId, false, false)
		//if statusErr != nil {
		//	fmt.Println("update shop status failed", setStatus)
		//	return "update shop status failed", statusErr
		//}
		//fmt.Println("update shop status successful", setStatus)
		if updateErr := c.updateJob(Job, userID, 5); updateErr != nil {
			fmt.Printf("failed to update job to failed status: %v\n", updateErr)
		}
		err := c.UpdateClusterNamespaceStatuses(9, req.ClusterID, req.NamespaceID, userID)
		if err != nil {
			return nil, err
		}

		_, err = c.DnsService.HandleDnsAsync(accessToken, dto.HandleDnsRequest{
			NamespaceID: req.NamespaceID,
			ZoneID:      "-",
			Method:      "destroy",
		})
		if err != nil {
			return nil, err
		}

		return "terraform destroy successful", tfDeleteWorkspaceErr
	}
	if req.Method == "apply" {
		tfApply := exec.Command("terraform", "apply", "-auto-approve")
		tfApply.Dir = terraformDir
		tfApplyOutput, tfApplyErr := tfApply.CombinedOutput()
		if tfApplyErr != nil {
			fmt.Println("terraform apply failed", string(tfApplyOutput))
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			return "terraform apply failed", tfApplyErr
		}
		fmt.Println("terraform apply successful", string(tfApplyOutput))

		if updateErr := c.updateJob(Job, userID, 5); updateErr != nil {
			fmt.Printf("failed to update job to failed status: %v\n", updateErr)
		}
		err := c.UpdateClusterNamespaceStatuses(3, req.ClusterID, req.NamespaceID, userID)
		if err != nil {
			return nil, err
		}

		return "terraform apply successful", tfApplyErr
	}
	return "method not found", nil
}

func (c *OperationService) CreateClusterAsync(userID uint64, accessToken string, req dto.OperationCreateReq) (interface{}, error) {
	eventID := req.ClusterID

	var Job JobRequest

	if req.Method == "apply" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Create cluster",
			Description: "Create the cluster to digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "cluster",
			Action:      "create",
		}
	} else if req.Method == "destroy" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Destroy cluster",
			Description: "Destroy the cluster from digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "cluster",
			Action:      "delete",
		}
	} else if req.Method == "plan" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Plan cluster",
			Description: "Plan the cluster to digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "cluster",
			Action:      "plan",
		}
	}

	job, err := c.jobService.CreateJob(
		userID,
		Job.Name,
		Job.Description,
		Job.StatusID,
		Job.EventID,
		Job.Event,
		Job.Action,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create job: %v", err)
	}
	Job.ID = &job.ID

	// Return immediately with status message including job ID
	statusMsg := fmt.Sprintf("Cluster operation '%s' for cluster ID %d has been initiated and is running in background. Job ID: %d", req.Method, req.ClusterID, job.ID)

	// Execute the actual cluster operations in a goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Recovered from panic in CreateClusterAsync: %v\n", r)
			}
		}()

		result, err := c.executeClusterOperation(accessToken, req, Job, userID)
		if err != nil {
			fmt.Printf("Async cluster operation failed: %v\n", err)
		} else {
			fmt.Printf("Async cluster operation completed successfully: %v\n", result)
		}
	}()

	return statusMsg, nil
}

func (c *OperationService) executeClusterOperation(accessToken string, req dto.OperationCreateReq, Job JobRequest, userID uint64) (interface{}, error) {
	// Update job status to running (3)
	if err := c.updateJob(Job, userID, 3); err != nil {
		return nil, err
	}
	var statusID uint64
	if Job.Action == "create" {
		statusID = 2
	}
	if Job.Action == "plan" {
		statusID = 1
	}
	if Job.Action == "update" {
		statusID = 5
	}
	if Job.Action == "delete" {
		statusID = 7
	}
	if err := c.updateClusterStatus(req.ClusterID, statusID); err != nil {
		return nil, fmt.Errorf("failed to update cluster status: %v", err)
	}

	valueEndpoint := os.Getenv("TF_OPERATION_ENDPOINT")
	endpoint := fmt.Sprintf("%s/operations/%s", valueEndpoint, strconv.Itoa(int(req.ClusterID)))
	terraformDir := "./terraform/cluster"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/cluster"
	}

	workspaceName := fmt.Sprintf("cluster-%s", strconv.Itoa(int(req.ClusterID)))

	envDoToken := os.Getenv("DO_TOKEN")
	// export and get ENV
	setEnvEndpoint, setEnvAccessToken, setEnvDoToken := os.Setenv("TF_VAR_operation_endpoint", endpoint), os.Setenv("TF_VAR_access_token", accessToken), os.Setenv("TF_VAR_do_token", envDoToken)
	if setEnvEndpoint != nil {
		fmt.Println("set env endpoint failed", setEnvEndpoint)
		return "set env endpoint failed", setEnvEndpoint
	}
	if setEnvAccessToken != nil {
		fmt.Println("set env api token failed", setEnvAccessToken)
		return "set env api token failed", setEnvAccessToken
	}
	if setEnvDoToken != nil {
		fmt.Println("set env do token failed", setEnvDoToken)
		return "set env do token failed", setEnvDoToken
	}

	valueDB := os.Getenv("TF_BACKEND_DB")
	valueSchema := os.Getenv("TF_BACKEND_SCHEMA_CLUSTER")

	// Add Helm repositories before updating
	helmAddBitnami := exec.Command("helm", "repo", "add", "bitnami", "https://charts.bitnami.com/bitnami")
	helmAddBitnami.Dir = terraformDir
	helmAddBitnamiOutput, helmAddBitnamiErr := helmAddBitnami.CombinedOutput()
	if helmAddBitnamiErr != nil {
		fmt.Printf("helm repo add bitnami warning (might already exist): %s\n", string(helmAddBitnamiOutput))
	}

	// Add other common repositories if needed
	helmAddIngress := exec.Command("helm", "repo", "add", "ingress-nginx", "https://kubernetes.github.io/ingress-nginx")
	helmAddIngress.Dir = terraformDir
	helmAddIngressOutput, helmAddIngressErr := helmAddIngress.CombinedOutput()
	if helmAddIngressErr != nil {
		fmt.Printf("helm repo add ingress-nginx warning (might already exist): %s\n", string(helmAddIngressOutput))
	}

	// Update helm repositories (correct command)
	helmUpdate := exec.Command("helm", "repo", "update")
	helmUpdate.Dir = terraformDir
	helmUpdateOutput, helmUpdateErr := helmUpdate.CombinedOutput()
	if helmUpdateErr != nil {
		fmt.Println("helm repo update failed", string(helmUpdateOutput))
		return "helm repo update failed", helmUpdateErr
	}
	fmt.Println("helm repo update successful", string(helmUpdateOutput))

	// terraform init
	connStr := fmt.Sprintf("-backend-config=conn_str=%s", valueDB)
	schemaName := fmt.Sprintf("-backend-config=schema_name=%s", valueSchema)
	tfInit := exec.Command("terraform", "init", "-lock=false", connStr, schemaName)
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()
	if tfInitErr != nil {
		fmt.Printf("terraform init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform init failed - error: %v\n", tfInitErr)

		// Update job status to failed (4) when terraform init fails
		if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
			fmt.Printf("failed to update job to failed status: %v\n", updateErr)
		}
		return "terraform init failed", tfInitErr
	}
	fmt.Println("terraform init successful", string(tfInitOutput))

	// terraform select workspace or create workspace
	tfWorkspace := exec.Command("terraform", "workspace", "select", "-or-create", workspaceName)
	tfWorkspace.Dir = terraformDir
	tfWorkspaceOutput, tfWorkspaceErr := tfWorkspace.CombinedOutput()
	if tfWorkspaceErr != nil {
		fmt.Println("select or create workspace failed", string(tfWorkspaceOutput))
		return "select or create workspace failed", tfWorkspaceErr
	}
	fmt.Println("select or create workspace successful", string(tfWorkspaceOutput))

	// terraform plan
	if req.Method == "plan" {
		tfPlan := exec.Command("terraform", "plan")
		tfPlan.Dir = terraformDir
		tfPlanOutput, tfPlanErr := tfPlan.CombinedOutput()
		if tfPlanErr != nil {
			fmt.Println("terraform plan failed", string(tfPlanOutput))
			return "terraform plan failed", tfPlanErr
		}
		fmt.Println("terraform plan successful", string(tfPlanOutput))
		return "terraform plan successful", nil
	}

	// terraform destroy
	if req.Method == "destroy" {
		tfDestroyCmd := exec.Command("terraform", "destroy", "-auto-approve", "-lock=false")
		tfDestroyCmd.Dir = terraformDir
		tfDestroyOutput, tfDestroyErr := tfDestroyCmd.CombinedOutput()
		if tfDestroyErr != nil {
			fmt.Println("terraform destroy failed", string(tfDestroyOutput))

			// Update job status to failed (4) when terraform destroy fails
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			if err := c.updateClusterStatus(req.ClusterID, 8); err != nil {
				return nil, fmt.Errorf("failed to update cluster status: %v", err)
			}

			return "terraform destroy failed", tfDestroyErr
		}
		fmt.Println("terraform destroy successful", string(tfDestroyOutput))

		// terraform delete workspace
		tfDefaultWorkspace := exec.Command("terraform", "workspace", "select", "-or-create", "default")
		tfDefaultWorkspace.Dir = terraformDir
		tfDefaultWorkspaceOutput, tfDefaultWorkspaceErr := tfDefaultWorkspace.CombinedOutput()
		if tfDefaultWorkspaceErr != nil {
			fmt.Println("select or create default workspace failed", string(tfDefaultWorkspaceOutput))
			return "select or create default workspace failed", tfDefaultWorkspaceErr
		}
		fmt.Println("select or create default workspace successful", string(tfDefaultWorkspaceOutput))

		tfDeleteWorkspace := exec.Command("terraform", "workspace", "delete", workspaceName)
		tfDeleteWorkspace.Dir = terraformDir
		tfDeleteWorkspaceOutput, tfDeleteWorkspaceErr := tfDeleteWorkspace.CombinedOutput()
		if tfDeleteWorkspaceErr != nil {
			fmt.Println("delete workspace failed", string(tfDeleteWorkspaceOutput))
			return "delete workspace failed", tfDeleteWorkspaceErr
		}
		fmt.Println("delete workspace successful", string(tfDeleteWorkspaceOutput))

		// Update job status to completed (5) when terraform destroy is successful
		if err := c.updateJob(Job, userID, 5); err != nil {
			return nil, err
		}
		if err := c.updateClusterStatus(req.ClusterID, 9); err != nil {
			return nil, fmt.Errorf("failed to update cluster status: %v", err)
		}

		return "terraform destroy successful", nil
	}

	// terraform apply
	if req.Method == "apply" {
		tfApplyCmd := exec.Command("terraform", "apply", "-auto-approve")
		tfApplyCmd.Dir = terraformDir
		tfApplyOutput, tfApplyErr := tfApplyCmd.CombinedOutput()
		if tfApplyErr != nil {
			fmt.Println("terraform apply failed", string(tfApplyOutput))

			// Update job status to failed (4) when terraform apply fails
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			if err := c.updateClusterStatus(req.ClusterID, 8); err != nil {
				return nil, fmt.Errorf("failed to update cluster status: %v", err)
			}

			return "terraform apply failed", tfApplyErr
		}
		fmt.Println("terraform apply successful", string(tfApplyOutput))

		// Get Terraform outputs after successful apply
		outputs, outputErr := c.getTerraformOutputs(terraformDir)
		if outputErr != nil {
			fmt.Printf("Warning: Failed to get terraform outputs: %v\n", outputErr)
		} else {
			// You can access specific outputs like this:
			if loadBalancerIP, exists := outputs["load_balancer_ip"]; exists && !loadBalancerIP.Sensitive {
				fmt.Printf("Load Balancer IP: %v\n", loadBalancerIP.Value)

				// Convert the load balancer IP to string
				loadBalancerIPStr := fmt.Sprintf("%v", loadBalancerIP.Value)

				// Update the cluster with the load balancer IP
				updatedCluster, updateErr := c.clusterService.UpdateLoadBalancerIP(req.ClusterID, loadBalancerIPStr)
				if updateErr != nil {
					fmt.Printf("Warning: Failed to update cluster load balancer IP: %v\n", updateErr)
				} else {
					fmt.Printf("Successfully updated cluster %d with load balancer IP: %s\n", updatedCluster.ID, updatedCluster.LoadBalanceIP)
				}
			}
		}

		// Update job status to completed (5) when terraform apply is successful
		if err := c.updateJob(Job, userID, 5); err != nil {
			return nil, err
		}
		if err := c.updateClusterStatus(req.ClusterID, 3); err != nil {
			return nil, fmt.Errorf("failed to update cluster status: %v", err)
		}

		return "terraform apply successful", nil
	}

	return "method not found", nil
}

func (c *OperationService) updateJob(job JobRequest, userID uint64, statusID uint64) error {
	_, err := c.jobService.UpdateJob(
		*job.ID,
		userID,
		job.Name,
		job.Description,
		statusID,
		job.EventID,
		job.Event,
		job.Action,
	)
	if err != nil {
		return fmt.Errorf("failed to update job: %v", err)
	}
	return nil
}

func (s *OperationService) updateClusterStatus(clusterID uint64, statusID uint64) error {
	// Get the current cluster to preserve other fields
	cluster, err := s.clusterService.GetByID(clusterID)
	if err != nil {
		return fmt.Errorf("failed to get cluster: %v", err)
	}

	// Update the cluster with the new status while preserving other fields
	_, err = s.clusterService.Update(
		clusterID,
		cluster.Name,
		cluster.Region,
		cluster.PoolName,
		cluster.Size,
		cluster.NodeCount,
		cluster.WorkspaceID,
		statusID,
	)
	if err != nil {
		return fmt.Errorf("failed to update cluster status: %v", err)
	}

	return nil
}

func (s *OperationService) updateDeploymentStatus(deploymentID uint64, statusID uint64) error {
	// Get the current deployment to preserve other fields
	deployment, err := s.deploymentService.GetByID(deploymentID)
	if err != nil {
		return fmt.Errorf("failed to get deployment: %v", err)
	}

	// Update the deployment with the new status while preserving other fields
	_, err = s.deploymentService.Update(
		deploymentID,
		deployment.Name,
		deployment.Image,
		deployment.ContainerPort,
		deployment.Replicas,
		deployment.NamespaceID,
		statusID,
	)
	if err != nil {
		return fmt.Errorf("failed to update deployment status: %v", err)
	}

	return nil
}

func (s *OperationService) updateServiceStatus(serviceID uint64, statusID uint64) error {
	// Get the current service to preserve other fields
	service, err := s.serviceService.GetByID(serviceID)
	if err != nil {
		return fmt.Errorf("failed to get service: %v", err)
	}

	// Update the service with the new status while preserving other fields
	_, err = s.serviceService.Update(
		serviceID,
		service.Name,
		service.Port,
		service.TargetPort,
		service.Type,
		service.ClusterIP,
		service.ExternalIP,
		service.NamespaceID,
		service.DeploymentID,
		statusID,
	)
	if err != nil {
		return fmt.Errorf("failed to update service status: %v", err)
	}

	return nil
}

func (s *OperationService) updateIngressStatus(ingressID uint64, statusID uint64) error {
	// Get the current ingress to preserve other fields
	ingress, err := s.ingressService.GetByID(ingressID)
	if err != nil {
		return fmt.Errorf("failed to get ingress: %v", err)
	}

	// Update the ingress with the new status while preserving other fields
	_, err = s.ingressService.Update(
		ingressID,
		ingress.Name,
		ingress.Class,
		ingress.NamespaceID,
		statusID,
	)
	if err != nil {
		return fmt.Errorf("failed to update ingress status: %v", err)
	}

	return nil
}

func (s *OperationService) UpdateClusterNamespaceStatuses(statusID uint64, clusterID uint64, namespaceID uint64, userID uint64) error {
	// Get cluster data with specific namespace
	cluster, err := s.clusterRepo.FindByID(clusterID, namespaceID)
	if err != nil {
		return fmt.Errorf("failed to get cluster: %v", err)
	}
	if cluster == nil {
		return errors.New("cluster not found")
	}

	// Find the specific namespace
	var targetNamespace *domain.Namespace
	for _, namespace := range cluster.Namespaces {
		if namespace.ID == namespaceID {
			targetNamespace = &namespace
			break
		}
	}

	if targetNamespace == nil {
		return errors.New("namespace not found in cluster")
	}

	var updateErrors []string
	var jobAction domain.JobAction
	if statusID == 2 {
		jobAction = "create"
	}
	if statusID == 1 {
		jobAction = "plan"
	}
	if statusID == 5 || statusID == 3 {
		jobAction = "update"
	}
	if statusID == 7 || statusID == 9 {
		jobAction = "delete"
	}

	// Update all deployments in the namespace
	for _, deployment := range targetNamespace.Deployments {
		var Job JobRequest
		Job = JobRequest{
			ID:          nil,
			Name:        fmt.Sprintf("%s deployment", jobAction),
			Description: fmt.Sprintf("%s the deployment to k8s", jobAction),
			StatusID:    1, // Creating
			EventID:     &deployment.ID,
			Event:       "deployment",
			Action:      jobAction,
		}
		if statusID != 9 && statusID != 3 {
			job, err := s.jobService.CreateJob(
				userID,
				Job.Name,
				Job.Description,
				Job.StatusID,
				Job.EventID,
				Job.Event,
				Job.Action,
			)
			if err != nil {
				return nil
			}
			Job.ID = &job.ID
		}
		if err := s.updateDeploymentStatus(deployment.ID, statusID); err != nil {
			if statusID != 9 && statusID != 3 {
				if err := s.updateJob(Job, userID, 4); err != nil {
					return nil
				}
			}
			updateErrors = append(updateErrors, fmt.Sprintf("deployment %d: %v", deployment.ID, err))
		}
		if statusID != 9 && statusID != 3 {
			if err := s.updateJob(Job, userID, 5); err != nil {
				return nil
			}
		}
	}

	// Update all services in the namespace
	for _, service := range targetNamespace.Services {
		var Job JobRequest
		Job = JobRequest{
			ID:          nil,
			Name:        fmt.Sprintf("%s service", jobAction),
			Description: fmt.Sprintf("%s the service to k8s", jobAction),
			StatusID:    1, // Creating
			EventID:     &service.ID,
			Event:       "service",
			Action:      jobAction,
		}
		if statusID != 9 && statusID != 3 {
			job, err := s.jobService.CreateJob(
				userID,
				Job.Name,
				Job.Description,
				Job.StatusID,
				Job.EventID,
				Job.Event,
				Job.Action,
			)
			if err != nil {
				return nil
			}
			Job.ID = &job.ID
		}
		if err := s.updateServiceStatus(service.ID, statusID); err != nil {
			if statusID != 9 && statusID != 3 {
				if err := s.updateJob(Job, userID, 4); err != nil {
					return nil
				}
			}
			updateErrors = append(updateErrors, fmt.Sprintf("service %d: %v", service.ID, err))
		}
		if statusID != 9 && statusID != 3 {
			if err := s.updateJob(Job, userID, 5); err != nil {
				return nil
			}
		}
	}

	// Update all ingresses in the namespace
	for _, ingress := range targetNamespace.Ingress {
		var Job JobRequest
		Job = JobRequest{
			ID:          nil,
			Name:        fmt.Sprintf("%s ingress", jobAction),
			Description: fmt.Sprintf("%s the ingress to k8s", jobAction),
			StatusID:    1, // Creating
			EventID:     &ingress.ID,
			Event:       "ingress",
			Action:      jobAction,
		}
		if statusID != 9 && statusID != 3 {
			job, err := s.jobService.CreateJob(
				userID,
				Job.Name,
				Job.Description,
				Job.StatusID,
				Job.EventID,
				Job.Event,
				Job.Action,
			)
			if err != nil {
				return nil
			}
			Job.ID = &job.ID
		}
		if err := s.updateIngressStatus(ingress.ID, statusID); err != nil {
			if statusID != 9 && statusID != 3 {
				if err := s.updateJob(Job, userID, 4); err != nil {
					return nil
				}
			}
			updateErrors = append(updateErrors, fmt.Sprintf("ingress %d: %v", ingress.ID, err))
		}
		if statusID != 9 && statusID != 3 {
			if err := s.updateJob(Job, userID, 5); err != nil {
				return nil
			}
		}
	}

	// Return combined errors if any occurred
	if len(updateErrors) > 0 {
		return fmt.Errorf("failed to update some statuses: %v", updateErrors)
	}

	// Update namespace type to published
	namespace, err := s.namespaceService.GetByID(namespaceID)
	if err != nil {
		return fmt.Errorf("failed to get namespace: %v", err)
	}

	_, err = s.namespaceService.Update(
		namespaceID,
		namespace.Name,
		namespace.Slug,
		namespace.IsActive,
		domain.NamespaceTypePublished,
		namespace.ClusterID,
	)
	if err != nil {
		return fmt.Errorf("failed to update namespace type to published: %v", err)
	}

	orderNamespaceFilter := ports.OrderNamespaceFilter{
		NamespaceID: &namespaceID,
	}
	orderNamespaces, err := s.orderNamespaceService.GetAll(&orderNamespaceFilter)
	if len(orderNamespaces) > 0 {
		for _, orderNamespace := range orderNamespaces {
			_, err := s.orderService.UpdateConfirmation(orderNamespace.OrderID, true)
			if err != nil {
				return fmt.Errorf("failed to update order confirmation: %v", err)
			}
		}
	}

	return nil
}

// getTerraformOutputs executes 'terraform output -json' and returns parsed outputs
func (c *OperationService) getTerraformOutputs(terraformDir string) (TerraformOutputs, error) {
	tfOutput := exec.Command("terraform", "output", "-json")
	tfOutput.Dir = terraformDir

	outputBytes, err := tfOutput.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("terraform output command failed: %v, output: %s", err, string(outputBytes))
	}

	// Handle empty outputs
	outputStr := strings.TrimSpace(string(outputBytes))
	if outputStr == "" || outputStr == "{}" {
		return TerraformOutputs{}, nil
	}

	var outputs TerraformOutputs
	if err := json.Unmarshal(outputBytes, &outputs); err != nil {
		return nil, fmt.Errorf("failed to parse terraform outputs JSON: %v, raw output: %s", err, string(outputBytes))
	}

	return outputs, nil
}
